{"training_configs": {"basic": {"epochs": 50, "batch_size": 32, "learning_rate": 0.001, "hidden_sizes": [64, 32], "dropout_rate": 0.2, "test_size": 0.2, "random_seed": 42}, "advanced": {"epochs": 150, "batch_size": 64, "learning_rate": 0.0005, "hidden_sizes": [256, 128, 64, 32], "dropout_rate": 0.3, "test_size": 0.2, "random_seed": 42}, "deep": {"epochs": 200, "batch_size": 128, "learning_rate": 0.0001, "hidden_sizes": [512, 256, 128, 64, 32], "dropout_rate": 0.4, "test_size": 0.2, "random_seed": 42}, "lightweight": {"epochs": 30, "batch_size": 16, "learning_rate": 0.01, "hidden_sizes": [32], "dropout_rate": 0.1, "test_size": 0.2, "random_seed": 42}}, "data_preprocessing": {"normalize_features": true, "handle_missing_values": "drop", "encode_categorical": true, "feature_selection": false}, "model_settings": {"activation_function": "relu", "output_activation": "softmax", "loss_function": "cross_entropy", "optimizer": "adam", "early_stopping": {"enabled": false, "patience": 10, "min_delta": 0.001}}, "logging": {"log_level": "INFO", "save_logs": true, "log_file": "training.log"}, "output": {"save_model": true, "save_plots": true, "save_history": true, "output_directory": "./outputs"}}