import pyttsx3

import json
import os
import random
from typing import Dict, List, Any, Union


class MinimalAI:
    

   
    
    def __init__(self, config_path: str = "poovizhi.json"):
        
        self.config = self._load_config(config_path)
        self.responses = self.config.get("responses", {})
        self.default_response = self.config.get("default_response", "I don't know how to respond to that.")
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"Warning: Config file {config_path} not found. Using default configuration.")
                return {"responses": {}, "default_response": "I don't know how to respond to that."}
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return {"responses": {}, "default_response": "I don't know how to respond to that."}
    
    def process(self, input_text: str) -> str:
        
      
        input_lower = input_text.lower().strip()
        
       
        if input_lower in self.responses:
            responses = self.responses[input_lower]
            if isinstance(responses, list):
                return random.choice(responses)
            return responses
        
       
        for key, value in self.responses.items():
            if key in input_lower:
                if isinstance(value, list):
                    return random.choice(value)
                return value
        
        
        return self.default_response
    
    def learn(self, input_text: str, response: Union[str, List[str]]) -> None:
        
        self.responses[input_text.lower().strip()] = response
    
    def save_config(self, config_path: str = "ai.json") -> None:
        
        config = {
            "responses": self.responses,
            "default_response": self.default_response
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
            print(f"Configuration saved to {config_path}")
        except Exception as e:
            print(f"Error saving configuration: {e}")



if __name__ == "__main__":
    ai = MinimalAI()
   
    speech=pyttsx3.init()
    
    rate = speech.getProperty('rate')
    speech.setProperty('rate', 125)
    volume = speech.getProperty('volume')
    speech.setProperty('volume',1.0) 
    print(" AI is ready. Type 'exit' to quit.")
    while True:
        user_input = input("> ")
        if user_input.lower() in ["exit", "quit", "bye"]:
            print("Goodbye!")
            break
        
        response = ai.process(user_input)
        print(response)
        a=response
        speech.say(a)
        speech.runAndWait()
       
        ai.save_config()