# Core ML and Deep Learning
torch>=1.9.0
torchvision>=0.10.0
scikit-learn>=1.0.0
numpy>=1.21.0
pandas>=1.3.0

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Jupyter and Interactive Development
jupyter>=1.0.0
ipywidgets>=7.6.0
notebook>=6.4.0

# Data Processing
scipy>=1.7.0
openpyxl>=3.0.0  # For Excel file support

# Optional: Advanced ML tools
# tensorboard>=2.7.0  # For advanced logging
# optuna>=2.10.0      # For hyperparameter optimization
# mlflow>=1.20.0      # For experiment tracking

# Development and Testing
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0

# System utilities
tqdm>=4.62.0  # Progress bars
argparse  # Command line parsing (built-in but listed for clarity)
