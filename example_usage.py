#!/usr/bin/env python3
"""
Example usage of the Neural Network Training Suite
This script demonstrates various ways to use the training tools
"""

import os
import json
import numpy as np
from neural_network_trainer import NeuralNetworkTrainer, create_sample_data

def example_1_basic_training():
    """Example 1: Basic training with default settings"""
    print("=" * 50)
    print("Example 1: Basic Training")
    print("=" * 50)
    
    # Create sample data
    print("Creating sample data...")
    create_sample_data('example_data.csv', n_samples=1000)
    
    # Basic configuration
    config = {
        'epochs': 50,
        'batch_size': 32,
        'learning_rate': 0.001,
        'hidden_sizes': [64, 32],
        'dropout_rate': 0.2,
        'test_size': 0.2,
        'random_seed': 42
    }
    
    # Train model
    trainer = NeuralNetworkTrainer(config)
    history = trainer.train('example_data.csv')
    
    # Save results
    trainer.save_model('example1_model.pth')
    trainer.plot_training_history('example1_history.png')
    
    print(f"Training completed!")
    print(f"Final accuracy: {history['accuracy'][-1]:.2f}%")
    print(f"Best accuracy: {max(history['accuracy']):.2f}%")
    print()

def example_2_advanced_training():
    """Example 2: Advanced training with deeper network"""
    print("=" * 50)
    print("Example 2: Advanced Training")
    print("=" * 50)
    
    # Advanced configuration
    config = {
        'epochs': 100,
        'batch_size': 64,
        'learning_rate': 0.0005,
        'hidden_sizes': [256, 128, 64, 32],
        'dropout_rate': 0.3,
        'test_size': 0.2,
        'random_seed': 42
    }
    
    # Train model
    trainer = NeuralNetworkTrainer(config)
    history = trainer.train('example_data.csv')
    
    # Save results
    trainer.save_model('example2_model.pth')
    trainer.plot_training_history('example2_history.png')
    
    print(f"Advanced training completed!")
    print(f"Final accuracy: {history['accuracy'][-1]:.2f}%")
    print(f"Best accuracy: {max(history['accuracy']):.2f}%")
    print()

def example_3_hyperparameter_comparison():
    """Example 3: Compare different hyperparameters"""
    print("=" * 50)
    print("Example 3: Hyperparameter Comparison")
    print("=" * 50)
    
    # Different configurations to test
    configs = [
        {
            'name': 'Small Network',
            'config': {
                'epochs': 30,
                'batch_size': 32,
                'learning_rate': 0.01,
                'hidden_sizes': [32],
                'dropout_rate': 0.1,
                'test_size': 0.2,
                'random_seed': 42
            }
        },
        {
            'name': 'Medium Network',
            'config': {
                'epochs': 30,
                'batch_size': 64,
                'learning_rate': 0.001,
                'hidden_sizes': [128, 64],
                'dropout_rate': 0.2,
                'test_size': 0.2,
                'random_seed': 42
            }
        },
        {
            'name': 'Large Network',
            'config': {
                'epochs': 30,
                'batch_size': 128,
                'learning_rate': 0.0001,
                'hidden_sizes': [256, 128, 64],
                'dropout_rate': 0.3,
                'test_size': 0.2,
                'random_seed': 42
            }
        }
    ]
    
    results = []
    
    for exp in configs:
        print(f"Training {exp['name']}...")
        trainer = NeuralNetworkTrainer(exp['config'])
        history = trainer.train('example_data.csv')
        
        result = {
            'name': exp['name'],
            'final_accuracy': history['accuracy'][-1],
            'best_accuracy': max(history['accuracy']),
            'final_loss': history['loss'][-1],
            'config': exp['config']
        }
        results.append(result)
        
        # Save individual model
        model_name = exp['name'].lower().replace(' ', '_')
        trainer.save_model(f'{model_name}_model.pth')
    
    # Compare results
    print("\nComparison Results:")
    print("-" * 80)
    print(f"{'Model':<15} {'Final Acc':<12} {'Best Acc':<12} {'Final Loss':<12}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['name']:<15} {result['final_accuracy']:<12.2f} "
              f"{result['best_accuracy']:<12.2f} {result['final_loss']:<12.4f}")
    
    # Save comparison results
    with open('hyperparameter_comparison.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nComparison results saved to hyperparameter_comparison.json")
    print()

def example_4_custom_data_format():
    """Example 4: Working with custom data formats"""
    print("=" * 50)
    print("Example 4: Custom Data Format")
    print("=" * 50)
    
    # Create custom dataset with different characteristics
    np.random.seed(42)
    n_samples = 2000
    n_features = 15
    n_classes = 4
    
    # Generate more complex synthetic data
    X = np.random.randn(n_samples, n_features)
    
    # Create non-linear relationships
    y = np.zeros(n_samples)
    for i in range(n_samples):
        # Complex decision boundary
        score = (X[i, 0] * X[i, 1] + 
                X[i, 2] ** 2 - 
                X[i, 3] * X[i, 4] + 
                np.sin(X[i, 5]) * X[i, 6])
        
        if score > 1:
            y[i] = 0
        elif score > 0:
            y[i] = 1
        elif score > -1:
            y[i] = 2
        else:
            y[i] = 3
    
    # Save as numpy array
    data = np.column_stack([X, y])
    np.save('custom_data.npy', data)
    
    print(f"Created custom dataset: {X.shape[0]} samples, {X.shape[1]} features, {n_classes} classes")
    
    # Train on custom data
    config = {
        'epochs': 80,
        'batch_size': 64,
        'learning_rate': 0.001,
        'hidden_sizes': [128, 64, 32],
        'dropout_rate': 0.25,
        'test_size': 0.2,
        'random_seed': 42
    }
    
    trainer = NeuralNetworkTrainer(config)
    history = trainer.train('custom_data.npy')
    
    trainer.save_model('custom_data_model.pth')
    trainer.plot_training_history('custom_data_history.png')
    
    print(f"Custom data training completed!")
    print(f"Final accuracy: {history['accuracy'][-1]:.2f}%")
    print(f"Best accuracy: {max(history['accuracy']):.2f}%")
    print()

def example_5_configuration_file():
    """Example 5: Using configuration files"""
    print("=" * 50)
    print("Example 5: Configuration File Usage")
    print("=" * 50)
    
    # Create a custom configuration file
    custom_config = {
        'epochs': 60,
        'batch_size': 48,
        'learning_rate': 0.0008,
        'hidden_sizes': [200, 100, 50],
        'dropout_rate': 0.35,
        'test_size': 0.25,
        'random_seed': 123
    }
    
    # Save configuration
    with open('custom_config.json', 'w') as f:
        json.dump(custom_config, f, indent=2)
    
    print("Created custom configuration file: custom_config.json")
    
    # Load and use configuration
    with open('custom_config.json', 'r') as f:
        loaded_config = json.load(f)
    
    trainer = NeuralNetworkTrainer(loaded_config)
    history = trainer.train('example_data.csv')
    
    trainer.save_model('config_based_model.pth')
    trainer.plot_training_history('config_based_history.png')
    
    print(f"Configuration-based training completed!")
    print(f"Final accuracy: {history['accuracy'][-1]:.2f}%")
    print(f"Best accuracy: {max(history['accuracy']):.2f}%")
    print()

def cleanup_example_files():
    """Clean up example files"""
    files_to_remove = [
        'example_data.csv', 'custom_data.npy', 'custom_config.json',
        'example1_model.pth', 'example2_model.pth', 'config_based_model.pth',
        'small_network_model.pth', 'medium_network_model.pth', 'large_network_model.pth',
        'custom_data_model.pth', 'hyperparameter_comparison.json',
        'example1_history.png', 'example2_history.png', 'config_based_history.png',
        'custom_data_history.png', 'training.log'
    ]
    
    print("Cleaning up example files...")
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"Removed: {file}")
    print("Cleanup completed!")

def main():
    """Run all examples"""
    print("Neural Network Training Suite - Example Usage")
    print("=" * 60)
    
    try:
        # Run examples
        example_1_basic_training()
        example_2_advanced_training()
        example_3_hyperparameter_comparison()
        example_4_custom_data_format()
        example_5_configuration_file()
        
        print("All examples completed successfully!")
        print("\nGenerated files:")
        print("- Model files: *.pth")
        print("- Training plots: *_history.png")
        print("- Configuration: custom_config.json")
        print("- Comparison results: hyperparameter_comparison.json")
        print("- Training logs: training.log")
        
        # Ask if user wants to clean up
        response = input("\nDo you want to clean up example files? (y/n): ")
        if response.lower() in ['y', 'yes']:
            cleanup_example_files()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have installed all required dependencies:")
        print("pip install -r requirements.txt")

if __name__ == '__main__':
    main()
