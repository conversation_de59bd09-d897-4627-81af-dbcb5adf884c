# Install required packages if not already installed
import subprocess
import sys

def install_package(package):
    try:
        __import__(package)
    except ImportError:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# Install required packages
packages = ['torch', 'torchvision', 'scikit-learn', 'pandas', 'matplotlib', 'seaborn', 'numpy']
for package in packages:
    install_package(package)

# Import libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import warnings
warnings.filterwarnings('ignore')

# Import our custom trainer
from neural_network_trainer import NeuralNetwork<PERSON>rainer, SimpleNeuralNetwork, create_sample_data

# Set style for plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("All imports successful!")
print(f"PyTorch version: {torch.__version__}")
print(f"Device available: {'CUDA' if torch.cuda.is_available() else 'CPU'}")

# Option 1: Create sample data for demonstration
create_sample_data('sample_data.csv', n_samples=2000)

# Load the data
data = pd.read_csv('sample_data.csv')
print(f"Data shape: {data.shape}")
print(f"\nFirst few rows:")
data.head()

# Option 2: Load your own data (uncomment and modify as needed)
# data = pd.read_csv('your_data.csv')
# print(f"Data shape: {data.shape}")
# data.head()

# Data exploration
print("Dataset Info:")
print(f"Shape: {data.shape}")
print(f"\nTarget distribution:")
print(data['target'].value_counts())

# Visualize target distribution
plt.figure(figsize=(8, 5))
data['target'].value_counts().plot(kind='bar')
plt.title('Target Class Distribution')
plt.xlabel('Class')
plt.ylabel('Count')
plt.xticks(rotation=0)
plt.show()

# Check for missing values
print(f"\nMissing values: {data.isnull().sum().sum()}")

# Feature correlation heatmap
plt.figure(figsize=(12, 8))
correlation_matrix = data.corr()
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
plt.title('Feature Correlation Matrix')
plt.tight_layout()
plt.show()

# Define training configuration
config = {
    'epochs': 150,
    'batch_size': 64,
    'learning_rate': 0.001,
    'hidden_sizes': [128, 64, 32],  # Three hidden layers
    'dropout_rate': 0.3,
    'test_size': 0.2,
    'random_seed': 42
}

print("Training Configuration:")
for key, value in config.items():
    print(f"  {key}: {value}")

# Initialize trainer
trainer = NeuralNetworkTrainer(config)

# Train the model
print("Starting training...")
history = trainer.train('sample_data.csv')
print("Training completed!")

# Plot training history
trainer.plot_training_history('training_history.png')

# More detailed training analysis
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Training loss
axes[0, 0].plot(history['loss'], 'b-', linewidth=2)
axes[0, 0].set_title('Training Loss Over Time', fontsize=14)
axes[0, 0].set_xlabel('Epoch')
axes[0, 0].set_ylabel('Loss')
axes[0, 0].grid(True, alpha=0.3)

# Training accuracy
axes[0, 1].plot(history['accuracy'], 'g-', linewidth=2)
axes[0, 1].set_title('Training Accuracy Over Time', fontsize=14)
axes[0, 1].set_xlabel('Epoch')
axes[0, 1].set_ylabel('Accuracy (%)')
axes[0, 1].grid(True, alpha=0.3)

# Loss smoothed (moving average)
window_size = 10
if len(history['loss']) > window_size:
    smoothed_loss = pd.Series(history['loss']).rolling(window=window_size).mean()
    axes[1, 0].plot(smoothed_loss, 'r-', linewidth=2)
    axes[1, 0].set_title(f'Smoothed Training Loss (window={window_size})', fontsize=14)
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Loss')
    axes[1, 0].grid(True, alpha=0.3)

# Accuracy smoothed (moving average)
if len(history['accuracy']) > window_size:
    smoothed_acc = pd.Series(history['accuracy']).rolling(window=window_size).mean()
    axes[1, 1].plot(smoothed_acc, 'orange', linewidth=2)
    axes[1, 1].set_title(f'Smoothed Training Accuracy (window={window_size})', fontsize=14)
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Accuracy (%)')
    axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print final statistics
print(f"\nFinal Training Statistics:")
print(f"Final Loss: {history['loss'][-1]:.4f}")
print(f"Final Accuracy: {history['accuracy'][-1]:.2f}%")
print(f"Best Accuracy: {max(history['accuracy']):.2f}%")
print(f"Best Accuracy Epoch: {np.argmax(history['accuracy']) + 1}")

# Load test data and make predictions
# This is a simplified example - in practice, you'd have a separate test set

# Prepare data for evaluation
X = data.drop('target', axis=1).values
y = data['target'].values

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# Scale the test data
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Convert to tensors
X_test_tensor = torch.FloatTensor(X_test_scaled)

# Make predictions
trainer.model.eval()
with torch.no_grad():
    outputs = trainer.model(X_test_tensor)
    _, predicted = torch.max(outputs, 1)
    predictions = predicted.numpy()

# Calculate accuracy
test_accuracy = accuracy_score(y_test, predictions)
print(f"Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")

# Classification report
print("\nClassification Report:")
print(classification_report(y_test, predictions))

# Confusion Matrix
cm = confusion_matrix(y_test, predictions)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=np.unique(y), yticklabels=np.unique(y))
plt.title('Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.show()

# Calculate per-class accuracy
class_accuracies = cm.diagonal() / cm.sum(axis=1)
for i, acc in enumerate(class_accuracies):
    print(f"Class {i} Accuracy: {acc:.4f} ({acc*100:.2f}%)")

# Save the trained model
trainer.save_model('trained_model.pth')
print("Model saved successfully!")

# Save training configuration and results
import json

results = {
    'config': config,
    'final_train_accuracy': history['accuracy'][-1],
    'best_train_accuracy': max(history['accuracy']),
    'test_accuracy': test_accuracy * 100,
    'total_epochs': len(history['loss'])
}

with open('training_results.json', 'w') as f:
    json.dump(results, f, indent=2)

print("Training results saved to training_results.json")

# Experiment with different configurations
experiment_configs = [
    {'hidden_sizes': [64], 'learning_rate': 0.01, 'epochs': 50},
    {'hidden_sizes': [128, 64], 'learning_rate': 0.001, 'epochs': 50},
    {'hidden_sizes': [256, 128, 64], 'learning_rate': 0.0001, 'epochs': 50},
]

experiment_results = []

for i, exp_config in enumerate(experiment_configs):
    print(f"\nRunning Experiment {i+1}: {exp_config}")
    
    # Merge with base config
    full_config = config.copy()
    full_config.update(exp_config)
    
    # Train model
    exp_trainer = NeuralNetworkTrainer(full_config)
    exp_history = exp_trainer.train('sample_data.csv')
    
    # Store results
    experiment_results.append({
        'config': exp_config,
        'final_accuracy': exp_history['accuracy'][-1],
        'best_accuracy': max(exp_history['accuracy']),
        'final_loss': exp_history['loss'][-1]
    })

# Compare results
print("\nExperiment Results Comparison:")
for i, result in enumerate(experiment_results):
    print(f"Experiment {i+1}: Best Acc = {result['best_accuracy']:.2f}%, Final Loss = {result['final_loss']:.4f}")

# Example of how to load and preprocess your own data

def load_custom_data(filepath):
    """
    Template function for loading custom data
    Modify this according to your data format
    """
    # Example for CSV files
    if filepath.endswith('.csv'):
        df = pd.read_csv(filepath)
        
        # Handle missing values
        df = df.dropna()  # or df.fillna(method='mean') for numerical columns
        
        # Encode categorical variables if needed
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if col != 'target':  # assuming 'target' is your label column
                le = LabelEncoder()
                df[col] = le.fit_transform(df[col])
        
        return df
    
    else:
        raise ValueError("Unsupported file format")

# Example usage:
# custom_data = load_custom_data('your_dataset.csv')
# print(f"Custom data shape: {custom_data.shape}")

print("Custom data loading function defined. Modify as needed for your data format.")

print("🎉 Neural Network Training Complete!")
print("\n📊 Summary:")
print(f"   • Model Architecture: {config['hidden_sizes']}")
print(f"   • Training Epochs: {config['epochs']}")
print(f"   • Final Training Accuracy: {history['accuracy'][-1]:.2f}%")
print(f"   • Test Accuracy: {test_accuracy*100:.2f}%")

print("\n📁 Files Created:")
print("   • trained_model.pth - Saved model")
print("   • training_results.json - Training configuration and results")
print("   • training_history.png - Training plots")

print("\n🚀 Next Steps:")
print("   1. Try different architectures by modifying 'hidden_sizes'")
print("   2. Experiment with learning rates and batch sizes")
print("   3. Add regularization techniques (dropout, batch norm)")
print("   4. Try different optimizers (SGD, RMSprop, AdamW)")
print("   5. Implement early stopping to prevent overfitting")
print("   6. Use cross-validation for more robust evaluation")
print("   7. Load your own dataset and adapt the preprocessing")