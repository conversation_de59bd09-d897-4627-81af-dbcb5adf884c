#!/usr/bin/env python3
"""
Neural Network Training Module
Provides command line interface and programmatic access for neural network training
"""

import argparse
import json
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import logging

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available. Install with: pip install torch torchvision")

try:
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Scikit-learn not available. Install with: pip install scikit-learn")


class SimpleNeuralNetwork(nn.Module):
    """Simple feedforward neural network"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, dropout_rate: float = 0.2):
        super(SimpleNeuralNetwork, self).__init__()
        
        layers = []
        prev_size = input_size
        
        # Hidden layers
        for hidden_size in hidden_sizes:
            layers.append(nn.Linear(prev_size, hidden_size))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            prev_size = hidden_size
        
        # Output layer
        layers.append(nn.Linear(prev_size, output_size))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)


class NeuralNetworkTrainer:
    """Main trainer class for neural networks"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.training_history = {'loss': [], 'accuracy': []}
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('training.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_data(self, data_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """Load training data from file"""
        try:
            if data_path.endswith('.csv'):
                import pandas as pd
                df = pd.read_csv(data_path)
                # Assume last column is target
                X = df.iloc[:, :-1].values
                y = df.iloc[:, -1].values
            elif data_path.endswith('.npy'):
                data = np.load(data_path)
                X = data[:, :-1]
                y = data[:, -1]
            else:
                raise ValueError("Unsupported file format. Use .csv or .npy")
            
            self.logger.info(f"Loaded data: X shape {X.shape}, y shape {y.shape}")
            return X, y
        
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            raise
    
    def prepare_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[DataLoader, DataLoader]:
        """Prepare data for training"""
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=self.config.get('test_size', 0.2), 
            random_state=self.config.get('random_seed', 42)
        )
        
        # Scale features
        if self.scaler:
            X_train = self.scaler.fit_transform(X_train)
            X_test = self.scaler.transform(X_test)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.LongTensor(y_train.astype(int))
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.LongTensor(y_test.astype(int))
        
        # Create data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.config.get('batch_size', 32), 
            shuffle=True
        )
        test_loader = DataLoader(
            test_dataset, 
            batch_size=self.config.get('batch_size', 32), 
            shuffle=False
        )
        
        return train_loader, test_loader
    
    def create_model(self, input_size: int, output_size: int):
        """Create neural network model"""
        hidden_sizes = self.config.get('hidden_sizes', [64, 32])
        dropout_rate = self.config.get('dropout_rate', 0.2)
        
        self.model = SimpleNeuralNetwork(input_size, hidden_sizes, output_size, dropout_rate)
        
        # Setup optimizer and loss function
        learning_rate = self.config.get('learning_rate', 0.001)
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        self.criterion = nn.CrossEntropyLoss()
        
        self.logger.info(f"Created model with architecture: {input_size} -> {hidden_sizes} -> {output_size}")
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def evaluate(self, test_loader: DataLoader) -> Tuple[float, float]:
        """Evaluate model on test data"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                output = self.model(data)
                loss = self.criterion(output, target)
                total_loss += loss.item()
                
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        avg_loss = total_loss / len(test_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def train(self, data_path: str):
        """Main training loop"""
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch is required for training")
        
        # Load and prepare data
        X, y = self.load_data(data_path)
        train_loader, test_loader = self.prepare_data(X, y)
        
        # Create model
        input_size = X.shape[1]
        output_size = len(np.unique(y))
        self.create_model(input_size, output_size)
        
        # Training loop
        epochs = self.config.get('epochs', 100)
        best_accuracy = 0.0
        
        for epoch in range(epochs):
            train_loss, train_acc = self.train_epoch(train_loader)
            test_loss, test_acc = self.evaluate(test_loader)
            
            # Store history
            self.training_history['loss'].append(train_loss)
            self.training_history['accuracy'].append(train_acc)
            
            # Log progress
            if epoch % 10 == 0:
                self.logger.info(
                    f'Epoch {epoch}/{epochs}: '
                    f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '
                    f'Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.2f}%'
                )
            
            # Save best model
            if test_acc > best_accuracy:
                best_accuracy = test_acc
                self.save_model('best_model.pth')
        
        self.logger.info(f'Training completed. Best accuracy: {best_accuracy:.2f}%')
        return self.training_history
    
    def save_model(self, filepath: str):
        """Save trained model"""
        if self.model:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'config': self.config,
                'scaler': self.scaler
            }, filepath)
            self.logger.info(f'Model saved to {filepath}')
    
    def load_model(self, filepath: str):
        """Load trained model"""
        checkpoint = torch.load(filepath)
        self.config = checkpoint['config']
        self.scaler = checkpoint['scaler']
        
        # Recreate model architecture (you'll need to know input/output sizes)
        # This is a simplified version - in practice, you'd save architecture info
        self.logger.info(f'Model loaded from {filepath}')
    
    def plot_training_history(self, save_path: str = 'training_history.png'):
        """Plot training history"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Loss plot
        ax1.plot(self.training_history['loss'])
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.grid(True)
        
        # Accuracy plot
        ax2.plot(self.training_history['accuracy'])
        ax2.set_title('Training Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path)
        plt.show()
        self.logger.info(f'Training history plot saved to {save_path}')


def create_sample_data(filename: str = 'sample_data.csv', n_samples: int = 1000):
    """Create sample dataset for testing"""
    np.random.seed(42)
    
    # Generate synthetic classification data
    n_features = 10
    n_classes = 3
    
    X = np.random.randn(n_samples, n_features)
    # Create some pattern in the data
    y = ((X[:, 0] + X[:, 1] > 0).astype(int) + 
         (X[:, 2] + X[:, 3] > 0).astype(int)) % n_classes
    
    # Save as CSV
    import pandas as pd
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    df['target'] = y
    df.to_csv(filename, index=False)
    
    print(f"Sample data created: {filename}")
    print(f"Shape: {X.shape}, Classes: {n_classes}")


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(description='Neural Network Training Tool')
    parser.add_argument('--data', type=str, required=True, help='Path to training data')
    parser.add_argument('--config', type=str, help='Path to config JSON file')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--hidden-sizes', nargs='+', type=int, default=[64, 32], help='Hidden layer sizes')
    parser.add_argument('--dropout-rate', type=float, default=0.2, help='Dropout rate')
    parser.add_argument('--test-size', type=float, default=0.2, help='Test set size ratio')
    parser.add_argument('--random-seed', type=int, default=42, help='Random seed')
    parser.add_argument('--create-sample', action='store_true', help='Create sample dataset')
    parser.add_argument('--output-dir', type=str, default='./outputs', help='Output directory')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create sample data if requested
    if args.create_sample:
        create_sample_data('sample_data.csv')
        return
    
    # Load config from file or use command line args
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'learning_rate': args.learning_rate,
            'hidden_sizes': args.hidden_sizes,
            'dropout_rate': args.dropout_rate,
            'test_size': args.test_size,
            'random_seed': args.random_seed
        }
    
    # Initialize trainer and start training
    trainer = NeuralNetworkTrainer(config)
    
    try:
        history = trainer.train(args.data)
        
        # Save results
        trainer.save_model(os.path.join(args.output_dir, 'final_model.pth'))
        trainer.plot_training_history(os.path.join(args.output_dir, 'training_history.png'))
        
        # Save config and history
        with open(os.path.join(args.output_dir, 'config.json'), 'w') as f:
            json.dump(config, f, indent=2)
        
        with open(os.path.join(args.output_dir, 'training_history.json'), 'w') as f:
            json.dump(history, f, indent=2)
        
        print(f"\nTraining completed successfully!")
        print(f"Results saved to: {args.output_dir}")
        
    except Exception as e:
        print(f"Training failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
