# Neural Network Training Suite

A comprehensive neural network training toolkit with both command-line interface and Jupyter notebook support for interactive development.

## Features

- 🚀 **Easy-to-use command line interface**
- 📊 **Interactive Jupyter notebook for experimentation**
- 🔧 **Configurable neural network architectures**
- 📈 **Built-in visualization and monitoring**
- 💾 **Model saving and loading capabilities**
- 🎯 **Multiple training configurations**
- 📋 **Comprehensive logging and reporting**

## Installation

1. **Clone or download the files to your project directory**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation:**
   ```bash
   python neural_network_trainer.py --help
   ```

## Quick Start

### Command Line Usage

1. **Create sample data:**
   ```bash
   python neural_network_trainer.py --create-sample
   ```

2. **Train with default settings:**
   ```bash
   python neural_network_trainer.py --data sample_data.csv
   ```

3. **Train with custom parameters:**
   ```bash
   python neural_network_trainer.py \
     --data sample_data.csv \
     --epochs 100 \
     --batch-size 64 \
     --learning-rate 0.001 \
     --hidden-sizes 128 64 32 \
     --dropout-rate 0.3
   ```

4. **Train with configuration file:**
   ```bash
   python neural_network_trainer.py --data sample_data.csv --config config.json
   ```

### Jupyter Notebook Usage

1. **Start Jupyter:**
   ```bash
   jupyter notebook
   ```

2. **Open `network.ipynb`**

3. **Run cells step by step for interactive training**

## Configuration Options

### Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--data` | Path to training data (CSV/NPY) | Required |
| `--config` | Path to JSON config file | None |
| `--epochs` | Number of training epochs | 100 |
| `--batch-size` | Training batch size | 32 |
| `--learning-rate` | Learning rate for optimizer | 0.001 |
| `--hidden-sizes` | Hidden layer sizes (space-separated) | [64, 32] |
| `--dropout-rate` | Dropout rate for regularization | 0.2 |
| `--test-size` | Test set size ratio | 0.2 |
| `--random-seed` | Random seed for reproducibility | 42 |
| `--output-dir` | Output directory for results | ./outputs |
| `--create-sample` | Create sample dataset | False |

### Configuration File Format

Use `config.json` to define training configurations:

```json
{
  "epochs": 150,
  "batch_size": 64,
  "learning_rate": 0.001,
  "hidden_sizes": [128, 64, 32],
  "dropout_rate": 0.3,
  "test_size": 0.2,
  "random_seed": 42
}
```

## Data Format

### CSV Format
- Features in columns, one sample per row
- Last column should be the target/label
- Headers optional but recommended

Example:
```csv
feature_1,feature_2,feature_3,target
1.2,3.4,5.6,0
2.1,4.3,6.5,1
...
```

### NumPy Format
- 2D array with features and target
- Last column should be the target/label

## Output Files

After training, the following files are created in the output directory:

- `final_model.pth` - Trained PyTorch model
- `best_model.pth` - Best model during training
- `training_history.png` - Training plots
- `config.json` - Training configuration used
- `training_history.json` - Detailed training metrics
- `training.log` - Training logs

## Examples

### Example 1: Basic Classification
```bash
# Create sample data
python neural_network_trainer.py --create-sample

# Train basic model
python neural_network_trainer.py \
  --data sample_data.csv \
  --epochs 50 \
  --hidden-sizes 64 32
```

### Example 2: Deep Network
```bash
python neural_network_trainer.py \
  --data your_data.csv \
  --epochs 200 \
  --batch-size 128 \
  --learning-rate 0.0001 \
  --hidden-sizes 512 256 128 64 32 \
  --dropout-rate 0.4
```

### Example 3: Using Configuration File
```bash
python neural_network_trainer.py \
  --data your_data.csv \
  --config config.json \
  --output-dir ./my_experiment
```

## Jupyter Notebook Features

The `network.ipynb` notebook provides:

- **Interactive data exploration** with visualizations
- **Step-by-step training process** with real-time monitoring
- **Hyperparameter experimentation** tools
- **Model evaluation** and testing
- **Custom data loading** examples
- **Advanced visualization** of training progress

## Customization

### Adding New Architectures

Modify the `SimpleNeuralNetwork` class in `neural_network_trainer.py`:

```python
class CustomNeuralNetwork(nn.Module):
    def __init__(self, input_size, output_size):
        super().__init__()
        # Define your custom architecture
        self.layers = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3),
            # Add more layers...
            nn.Linear(128, output_size)
        )
    
    def forward(self, x):
        return self.layers(x)
```

### Custom Data Preprocessing

Extend the `load_data` method in `NeuralNetworkTrainer`:

```python
def custom_preprocessing(self, data):
    # Add your preprocessing steps
    # Feature scaling, encoding, etc.
    return processed_data
```

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size
2. **Poor convergence**: Try different learning rates
3. **Overfitting**: Increase dropout rate or reduce model size
4. **Underfitting**: Increase model capacity or training epochs

### Performance Tips

- Use GPU if available: `torch.cuda.is_available()`
- Optimize batch size for your hardware
- Use learning rate scheduling for better convergence
- Monitor training/validation loss to detect overfitting

## Requirements

- Python 3.7+
- PyTorch 1.9+
- scikit-learn 1.0+
- pandas, numpy, matplotlib
- Jupyter (for notebook usage)

## License

This project is open source. Feel free to modify and distribute.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the example configurations
3. Examine the Jupyter notebook for detailed usage
4. Create an issue with detailed error information
